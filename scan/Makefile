# Modern CUDA Makefile for Scan
# Supports automatic GPU architecture detection and modern CUDA features

# Project configuration
EXECUTABLE := cudaScan
CU_FILES   := scan.cu
CC_FILES   := main.cpp
OBJDIR     := objs
LOGS       := logs

# Build configuration
BUILD_MODE ?= release
DEBUG      ?= 0

# Compiler detection
NVCC := $(shell which nvcc 2>/dev/null)
ifeq ($(NVCC),)
    $(error NVCC not found. Please ensure CUDA is installed and nvcc is in PATH)
endif

# CUDA version detection
CUDA_VERSION := $(shell nvcc --version | grep "release" | sed 's/.*release \([0-9]\+\.[0-9]\+\).*/\1/')
CUDA_MAJOR := $(shell echo $(CUDA_VERSION) | cut -d. -f1)
CUDA_MINOR := $(shell echo $(CUDA_VERSION) | cut -d. -f2)

# Architecture detection
ARCH := $(shell uname -m)
OS := $(shell uname -s)

# GPU architecture auto-detection
# Try to detect GPU compute capability automatically
GPU_ARCH := $(shell nvidia-smi --query-gpu=compute_cap --format=csv,noheader,nounits 2>/dev/null | head -1 | tr -d '.')
ifeq ($(GPU_ARCH),)
    # Fallback to common architectures if detection fails
    GPU_ARCH := 86  # RTX 30xx series default
    $(warning Could not detect GPU architecture, using sm_$(GPU_ARCH) as default)
endif

# CUDA library path detection
CUDA_LIB_PATHS := \
    /opt/nvidia/hpc_sdk/Linux_$(ARCH)/25.3/cuda/12.8/targets/$(ARCH)-linux/lib \
    /opt/nvidia/hpc_sdk/Linux_$(ARCH)/2025/cuda/12.8/lib64 \
    /opt/nvidia/hpc_sdk/Linux_$(ARCH)/2025/lib64 \
    /usr/local/cuda/lib64 \
    /usr/local/cuda/lib \
    /opt/cuda/lib64 \
    /opt/cuda/lib

CUDA_LIB_PATH := $(firstword $(foreach path,$(CUDA_LIB_PATHS),$(wildcard $(path))))
ifeq ($(CUDA_LIB_PATH),)
    $(error Could not find CUDA libraries. Please check your CUDA installation)
endif

# Compiler flags
NVCC_FLAGS := -std=c++11 -Wno-deprecated-gpu-targets
CXX_FLAGS  := -std=c++11 -Wno-deprecated-gpu-targets

# Architecture-specific flags
ifeq ($(ARCH), x86_64)
    NVCC_FLAGS += -m64
    CXX_FLAGS  += -m64
endif

# Build mode flags
ifeq ($(BUILD_MODE), debug)
    NVCC_FLAGS += -g -G -O0 -DDEBUG
    CXX_FLAGS  += -g -O0 -DDEBUG
else
    NVCC_FLAGS += -O3 -DNDEBUG
    CXX_FLAGS  += -O3 -DNDEBUG
endif

# GPU architecture flags
NVCC_FLAGS += -arch=sm_$(GPU_ARCH)

# Additional useful flags
NVCC_FLAGS += -lineinfo  # Add line info for profiling
NVCC_FLAGS += --extended-lambda  # Enable device lambdas

# Linker flags
LDFLAGS := -L$(CUDA_LIB_PATH) -lcudart

# Object files
OBJS := $(addprefix $(OBJDIR)/, $(CC_FILES:.cpp=.o) $(CU_FILES:.cu=.o))

# Phony targets
.PHONY: all clean debug release info dirs test check_scan check_find_repeats

# Default target
all: $(EXECUTABLE)
default: $(EXECUTABLE)

# Debug build
debug:
	$(MAKE) BUILD_MODE=debug

# Release build
release:
	$(MAKE) BUILD_MODE=release

# Test with checker
test: $(EXECUTABLE)
	@echo "Running scan tests..."
	./checker.py scan

# Show build information
info:
	@echo "=== Build Configuration ==="
	@echo "CUDA Version:    $(CUDA_VERSION)"
	@echo "GPU Architecture: sm_$(GPU_ARCH)"
	@echo "CUDA Library:    $(CUDA_LIB_PATH)"
	@echo "Build Mode:      $(BUILD_MODE)"
	@echo "NVCC:           $(NVCC)"
	@echo "Architecture:    $(ARCH)"
	@echo "OS:             $(OS)"
	@echo "=========================="

# Create directories
dirs:
	@mkdir -p $(OBJDIR)

# Clean build artifacts
clean:
	rm -rf $(OBJDIR) *.ppm *~ $(EXECUTABLE) $(LOGS)

# Legacy check targets (preserved for compatibility)
check_scan: default
	./checker.py scan

check_find_repeats: default
	./checker.py find_repeats

# Main executable
$(EXECUTABLE): dirs $(OBJS)
	$(NVCC) $(CXX_FLAGS) -o $@ $(OBJS) $(LDFLAGS)

# C++ compilation
$(OBJDIR)/%.o: %.cpp
	$(NVCC) $(CXX_FLAGS) -c $< -o $@

# CUDA compilation
$(OBJDIR)/%.o: %.cu
	$(NVCC) $(NVCC_FLAGS) -c $< -o $@

# Dependency tracking (optional enhancement)
-include $(OBJS:.o=.d)
