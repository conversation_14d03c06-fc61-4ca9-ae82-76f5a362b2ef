
#ifndef __UTIL_H__
#define __UTIL_H__

#include <algorithm>

#define CLAMP(x, minimum, maximum) std::max(minimum, std::min(x, maximum))

#define DEBUG

#ifdef DEBUG
#ifdef __CUDACC__
#include <cuda_runtime.h>
#define cudaCheckError(ans) { cudaAssert((ans), __FILE__, __LINE__); }
inline void cudaAssert(cudaError_t code, const char *file, int line, bool abort=true)
{
   if (code != cudaSuccess)
   {
      fprintf(stderr, "CUDA Error: %s at %s:%d\n",
        cudaGetErrorString(code), file, line);
      if (abort) exit(code);
   }
}
#else
#define cudaCheckError(ans) ans
#endif
#else
#define cudaCheckError(ans) ans
#endif

#endif
