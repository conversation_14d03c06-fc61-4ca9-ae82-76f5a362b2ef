#include "noise.h"


/**
 * This is the table of permutations required by the noise functions to
 * hash their input to generate the X coordinates.
 */
static int NoiseXPermutationTable[] = {
    76, 97, 84, 215, 37, 4, 251, 48, 100, 174, 161, 125, 136, 99, 139, 19,
    101, 158, 202, 165, 138, 129, 144, 180, 232, 196, 155, 252, 182, 173,
    55, 201, 189, 14, 225, 31, 171, 46, 79, 146, 217, 127, 41, 92, 191, 82,
    183, 87, 65, 212, 108, 43, 24, 27, 111, 206, 9, 132, 13, 224, 77, 102,
    120, 194, 1, 107, 60, 38, 88, 90, 121, 163, 3, 137, 66, 181, 184, 227,
    122, 152, 233, 71, 59, 104, 133, 142, 160, 75, 236, 177, 78, 179, 12,
    74, 135, 126, 213, 51, 115, 243, 188, 164, 230, 114, 223, 208, 209, 68,
    130, 176, 190, 148, 53, 110, 96, 169, 105, 166, 150, 231, 131, 36, 241,
    62, 30, 56, 255, 221, 6, 123, 207, 72, 245, 134, 15, 54, 145, 193, 5,
    254, 98, 204, 175, 57, 205, 222, 186, 109, 229, 17, 45, 119, 117, 151,
    29, 49, 32, 22, 198, 140, 94, 253, 154, 195, 10, 159, 50, 23, 8, 86,
    95, 187, 25, 242, 214, 7, 203, 143, 147, 67, 64, 81, 73, 16, 21, 116,
    167, 244, 34, 200, 250, 44, 239, 33, 170, 113, 185, 85, 18, 40, 91,
    168, 89, 235, 238, 156, 172, 63, 249, 197, 35, 0, 28, 234, 237, 2, 210,
    247, 178, 103, 52, 141, 228, 70, 218, 112, 226, 248, 106, 157, 20, 83,
    118, 240, 128, 11, 211, 153, 42, 149, 216, 93, 69, 61, 220, 47, 199,
    219, 124, 80, 26, 58, 162, 39, 246, 192
};

/**
 * This is the table of permutations required by the noise functions to
 * hash their input to generate the Y coordinates.
 */
static int NoiseYPermutationTable[] = {
    105, 76, 135, 3, 66, 169, 252, 41, 40, 69, 26, 141, 91, 88, 75, 191,
    118, 184, 126, 110, 172, 64, 185, 142, 157, 85, 39, 113, 56, 192, 70,
    111, 170, 87, 177, 100, 186, 216, 127, 94, 136, 182, 200, 37, 241, 251,
    128, 9, 161, 237, 32, 219, 44, 211, 20, 217, 35, 245, 42, 68, 218, 130,
    73, 90, 209, 228, 249, 7, 61, 102, 109, 17, 92, 116, 201, 208, 171,
    238, 96, 2, 62, 4, 223, 206, 106, 19, 86, 99, 214, 46, 38, 52, 83, 240,
    222, 150, 89, 204, 47, 231, 79, 114, 11, 49, 188, 193, 159, 72, 227,
    81, 235, 108, 25, 156, 230, 120, 80, 27, 225, 115, 220, 158, 13, 244,
    121, 55, 97, 5, 246, 67, 254, 233, 23, 194, 181, 146, 21, 236, 34, 212,
    104, 199, 122, 95, 163, 18, 15, 14, 167, 178, 143, 36, 58, 139, 229,
    205, 74, 48, 190, 60, 215, 93, 234, 162, 71, 82, 78, 232, 33, 103, 239,
    28, 63, 138, 189, 203, 210, 147, 57, 180, 207, 165, 145, 137, 198, 253,
    195, 45, 166, 77, 12, 134, 123, 221, 154, 129, 22, 50, 0, 152, 101,
    197, 226, 98, 247, 124, 117, 65, 10, 1, 107, 243, 16, 168, 54, 196,
    202, 183, 30, 133, 112, 125, 179, 31, 248, 176, 213, 140, 84, 24, 164,
    132, 51, 131, 255, 144, 8, 224, 173, 153, 242, 160, 29, 43, 149, 6,
    155, 148, 175, 53, 151, 250, 174, 59, 187, 119
};

/**
 * A table of 256 random values, evenly distributed between -1.0 and 1.0.
 * The values in the noise permutation tables can be used to index into
 * this table to get the gradients for the noise.
 */
static float Noise1DValueTable[] = {
    0.47451, 0.482353, 0.670588, 0.545098, 0.803922, -0.443137, -0.678431,
    0.32549, 0.45098, 0.396078, 0.905882, -0.0666667, -0.466667, 0.733333,
    0.87451, 0.568627, 0.921569, -0.584314, 0.262745, -0.670588, 0.254902,
    0.0745098, 0.647059, 0.654902, 0.858824, 0.00392157, 0.796078,
    0.333333, -0.835294, 0.498039, 0.0431373, -0.960784, 0.0117647,
    -0.286275, -0.858824, -0.717647, 0.207843, -0.639216, 0.458824,
    0.121569, 0.835294, 0.278431, -0.545098, -0.819608, 0.341176, 0.85098,
    -0.262745, -0.568627, -0.890196, 0.160784, 0.592157, 0.223529,
    -0.662745, 0.490196, 0.72549, 0.537255, -0.403922, -0.709804,
    -0.701961, -0.513726, -0.984314, 0.662745, 0.364706, -0.239216,
    -0.380392, 0.247059, -0.278431, -0.372549, -0.898039, 0.231373,
    0.992157, 0.937255, -0.647059, 0.215686, -0.34902, 0.0980392, 0.694118,
    -0.552941, -0.694118, -0.364706, -0.129412, 0.709804, 0.882353,
    -0.537255, -0.576471, 0.717647, 0.435294, 0.356863, -0.623529,
    -0.0431373, 1.0, -0.85098, -0.168627, -0.654902, -0.215686, -1.0,
    -0.411765, -0.396078, 0.443137, 0.607843, -0.027451, 0.0666667,
    0.380392, -0.0745098, 0.984314, -0.0509804, 0.929412, -0.811765,
    -0.764706, 0.137255, 0.239216, -0.32549, -0.113725, 0.411765, 0.576471,
    -0.160784, -0.803922, 0.301961, -0.796078, -0.0117647, 0.811765, 0.6,
    -0.921569, -0.6, 0.952941, -0.780392, 0.756863, -0.0823529, -0.968627,
    0.309804, 0.521569, -0.521569, 0.819608, 0.827451, -0.145098, 0.960784,
    0.168627, 0.372549, -0.992157, -0.0901961, 0.976471, -0.231373,
    -0.47451, -0.458824, -0.388235, 0.427451, 0.513726, -0.0196078,
    -0.74902, -0.270588, -0.137255, 0.945098, -0.843137, 0.184314,
    -0.905882, -0.301961, 0.615686, -0.333333, -0.482353, 0.466667,
    0.788235, -0.254902, 0.772549, -0.772549, 0.145098, 0.403922, 0.286275,
    -0.223529, -0.247059, 0.0901961, -0.741176, 0.388235, -0.0588235,
    -0.615686, -0.937255, 0.0588235, -0.631373, 0.270588, 0.560784,
    0.898039, 0.113725, -0.435294, 0.631373, 0.686275, -0.882353,
    -0.341176, 0.505882, -0.294118, -0.756863, 0.529412, -0.121569,
    0.027451, 0.584314, -0.317647, -0.866667, 0.0196078, 0.317647,
    -0.309804, -0.105882, -0.00392157, -0.945098, 0.152941, 0.678431,
    0.552941, 0.741176, -0.592157, -0.0980392, -0.152941, -0.827451,
    0.419608, 0.192157, -0.176471, 0.639216, -0.2, -0.952941, 0.0352941,
    0.890196, 0.780392, -0.929412, -0.490196, 0.866667, -0.788235,
    0.764706, -0.419608, 0.34902, 0.623529, -0.87451, -0.976471, 0.0509804,
    -0.184314, -0.427451, -0.45098, -0.607843, 0.129412, 0.294118,
    -0.192157, -0.686275, -0.356863, 0.701961, 0.74902, 0.913725, 0.843137,
    -0.560784, 0.968627, -0.72549, 0.0823529, 0.2, 0.176471, -0.505882,
    -0.207843, -0.733333, -0.913725, -0.498039, 0.105882, -0.0352941,
        -0.529412
};


void
vec2CellNoise(float location[3], float result[2], int index)
{
    int integer_of_x = static_cast<int>( location[0] );
    int integer_of_y = static_cast<int>( location[1] );
    int integer_of_z = static_cast<int>( location[2] );
    int hash = NoiseXPermutationTable[ (integer_of_x*index) & 0xFF ];
    hash = NoiseXPermutationTable[ ( hash + integer_of_y ) & 0xFF ];
    hash = NoiseXPermutationTable[ ( hash + integer_of_z ) & 0xFF ];
    float x_result = Noise1DValueTable[ hash ];
    hash = NoiseYPermutationTable[ integer_of_x & 0xFF ];
    hash = NoiseYPermutationTable[ ( hash + integer_of_y ) & 0xFF ];
    hash = NoiseYPermutationTable[ ( hash + integer_of_z ) & 0xFF ];
    float y_result = Noise1DValueTable[ hash ];
    //hash = NoiseZPermutationTable[ integer_of_x & 0xFF ];
    //hash = NoiseZPermutationTable[ ( hash + integer_of_y ) & 0xFF ];
    //hash = NoiseZPermutationTable[ ( hash + integer_of_z ) & 0xFF ];
    //float z_result = Noise1DValueTable[ hash ];
    result[0] = x_result;
    result[1] = y_result;
    //result[2] = z_result;
}

void
getNoiseTables(int** permX, int** permY, float** value1D) {
    *permX = NoiseXPermutationTable;
    *permY = NoiseYPermutationTable;
    *value1D = Noise1DValueTable;
}

