Performance of reference implementation on all scenes:
(All timings are in millseconds)

Tests run using benchmark mode flag --bench 0:4
(Reported times are per-frame time for just the call to render())

                 image size: 512x512           image size: 1024x1024
                     ref    cuda (speedup)          ref      cuda (speedup)
--------------------------------------------------------------------------
rgb                 1.94    0.13 (14.9x)           8.02      0.49 (16.4x)
rgby                1.05    0.12  (8.8x)           4.31      0.46  (9.4x)    
pattern             4.32    0.49  (8.8x)          18.86      1.76 (10.7x)
rand10k           208.40    5.86 (35.6x)         882.75     21.26 (41.5x)
rand100k         2084.03   60.47 (41.3x)        8860.17    217.72 (40.7x)
snowsingle        255.55   29.72  (8.6x         1006.35    113.96  (8.8x)

