{"files.associations": {"functional": "cpp", "random": "cpp", "*.cu": "cpp", "*.cuh": "cpp", "new": "cpp", "thread": "cpp", "algorithm": "cpp", "*.cu_inl": "cpp", "streambuf": "cpp", "vector": "cpp", "string": "cpp", "cstring": "cpp", "numeric": "cpp"}, "C_Cpp.default.includePath": ["${workspaceFolder}/**", "/usr/local/cuda/include", "/opt/nvidia/hpc_sdk/Linux_x86_64/2025/cuda/include"], "C_Cpp.default.compilerPath": "/usr/bin/g++", "C_Cpp.default.cppStandard": "c++14", "C_Cpp.default.intelliSenseMode": "linux-gcc-x64"}