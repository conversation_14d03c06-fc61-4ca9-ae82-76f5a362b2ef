#include <stdlib.h>
#include <stdio.h>
#include <getopt.h>
#include <string>
#include <cuda_runtime.h>

double saxpyCuda(int N, float alpha, float* x, float* y, float* result);
void printCudaInfo();


void usage(const char* progname) {
    printf("Usage: %s [options]\n", progname);
    printf("Program Options:\n");
    printf("  -n  --arraysize <INT>  Number of elements in arrays\n");
    printf("  -?  --help             This message\n");
}


int main(int argc, char** argv)
{

    // default: arrays of 100M numbers
    int N = 100 * 1000 * 1000;

    // parse commandline options ////////////////////////////////////////////
    int opt;
    static struct option long_options[] = {
        {"arraysize",  1, 0, 'n'},
        {"help",       0, 0, '?'},
        {0 ,0, 0, 0}
    };

    while ((opt = getopt_long(argc, argv, "?n:", long_options, NULL)) != EOF) {

        switch (opt) {
        case 'n':
            N = atoi(optarg);
            break;
        case '?':
        default:
            usage(argv[0]);
            return 1;
        }
    }
    // end parsing of commandline options //////////////////////////////////////

    const float alpha = 2.0f;
    // float* xarray = new float[N];
    // float* yarray = new float[N];
    // float* resultarray = new float[N];
    float *xarray,  *yarray, *resultarray;
    cudaMallocHost(&xarray, sizeof(float) * N);
    cudaMallocHost(&yarray, sizeof(float) * N);
    cudaMallocHost(&resultarray, sizeof(float) * N);

    for (int i=0; i<N; i++) {
        xarray[i] = yarray[i] = i % 10;
        resultarray[i] = 0.f;
   }

    printCudaInfo();

    printf("Running 3 timing tests:\n");
    double totalTime = 0.0;
    double minTime = 1e9;
    double maxTime = 0.0;

    for (int i=0; i<3; i++) {
      printf("\n--- Test %d ---\n", i+1);
      double execTime = saxpyCuda(N, alpha, xarray, yarray, resultarray);
      totalTime += execTime;
      if (execTime < minTime) minTime = execTime;
      if (execTime > maxTime) maxTime = execTime;
    }

    // Calculate and print summary statistics
    double avgTime = totalTime / 3.0;
    int totalBytes = sizeof(float) * 3 * N;
    float avgBandwidth = static_cast<float>(totalBytes) / (1024.0 * 1024.0 * 1024.0) / avgTime;

    printf("\n=== SUMMARY STATISTICS ===\n");
    printf("Average execution time: %.3f ms\n", 1000.0 * avgTime);
    printf("Min execution time: %.3f ms\n", 1000.0 * minTime);
    printf("Max execution time: %.3f ms\n", 1000.0 * maxTime);
    printf("Average bandwidth: %.3f GB/s\n", avgBandwidth);

    cudaFreeHost(xarray);
    cudaFreeHost(yarray);
    cudaFreeHost(resultarray);

    return 0;
}
