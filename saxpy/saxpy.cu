#include <stdio.h>

#include <cuda.h>
#include <cuda_runtime.h>
#include <driver_functions.h>

#include "CycleTimer.h"


// return GB/sec
float GBPerSec(int bytes, float sec) {
  return static_cast<float>(bytes) / (1024. * 1024. * 1024.) / sec;
}

// Get GPU memory bandwidth
float getGPUMemoryBandwidth() {
    cudaDeviceProp deviceProps;
    cudaGetDeviceProperties(&deviceProps, 0);

    float memoryClockRate = deviceProps.memoryClockRate; // in kHz
    float memoryBusWidth = deviceProps.memoryBusWidth;   // in bits
    float gpuMemoryBW = (memoryClockRate * memoryBusWidth * 2.0f) / (8.0f * 1e6);

    return gpuMemoryBW;
}

// Get PCIe bandwidth estimate
float getPCIeBandwidth() {
    cudaDeviceProp deviceProps;
    cudaGetDeviceProperties(&deviceProps, 0);

    float pcieBandwidth;
    if (deviceProps.major >= 8) {
        // Modern GPUs (RTX 30/40 series) typically support PCIe 4.0
        pcieBandwidth = 25.0f; // GB/s - practical PCIe 4.0 x16 bandwidth
    } else if (deviceProps.major >= 6) {
        // GTX 10/20 series era, typically PCIe 3.0
        pcieBandwidth = 15.0f; // GB/s - practical PCIe 3.0 x16 bandwidth
    } else {
        // Older GPUs
        pcieBandwidth = 8.0f;  // GB/s - conservative estimate
    }

    return pcieBandwidth;
}


// This is the CUDA "kernel" function that is run on the GPU.  You
// know this because it is marked as a __global__ function.
__global__ void
saxpy_kernel(int N, float alpha, float* x, float* y, float* result) {

    // compute overall thread index from position of thread in current
    // block, and given the block we are in (in this example only a 1D
    // calculation is needed so the code only looks at the .x terms of
    // blockDim and threadIdx.
    int index = blockIdx.x * blockDim.x + threadIdx.x;


    // this check is necessary to make the code work for values of N
    // that are not a multiple of the thread block size (blockDim.x)
    if (index < N)
       result[index] = alpha * x[index] + y[index];
}


// saxpyCuda --
//
// This function is regular C code running on the CPU.  It allocates
// memory on the GPU using CUDA API functions, uses CUDA API functions
// to transfer data from the CPU's memory address space to GPU memory
// address space, and launches the CUDA kernel function on the GPU.
// Returns the execution time in seconds.
double saxpyCuda(int N, float alpha, float* xarray, float* yarray, float* resultarray) {

    // must read both input arrays (xarray and yarray) and write to
    // output array (resultarray)
    int totalBytes = sizeof(float) * 3 * N;

    // compute number of blocks and threads per block.  In this
    // application we've hardcoded thread blocks to contain 512 CUDA
    // threads.
    const int threadsPerBlock = 512;

    // Notice the round up here.  The code needs to compute the number
    // of threads blocks needed such that there is one thread per
    // element of the arrays.  This code is written to work for values
    // of N that are not multiples of threadPerBlock.
    const int blocks = (N + threadsPerBlock - 1) / threadsPerBlock;

    // These are pointers that will be pointers to memory allocated
    // *one the GPU*.  You should allocate these pointers via
    // cudaMalloc.  You can access the resulting buffers from CUDA
    // device kernel code (see the kernel function saxpy_kernel()
    // above) but you cannot access the contents these buffers from
    // this thread. CPU threads cannot issue loads and stores from GPU
    // memory!
    float* device_x = nullptr;
    float* device_y = nullptr;
    float* device_result = nullptr;
    
    //
    // CS149 TODO: allocate device memory buffers on the GPU using cudaMalloc.
    //
    // We highly recommend taking a look at NVIDIA's
    // tutorial, which clearly walks you through the few lines of code
    // you need to write for this part of the assignment:
    //
    // https://devblogs.nvidia.com/easy-introduction-cuda-c-and-c/
    //
        
    cudaMalloc(&device_x, sizeof(float) * N);
    cudaMalloc(&device_y, sizeof(float) * N);
    cudaMalloc(&device_result, sizeof(float) * N);

    // start timing after allocation of device memory
    double startTime = CycleTimer::currentSeconds();

    //
    // CS149 TODO: copy input arrays to the GPU using cudaMemcpy
    //
    cudaMemcpy(device_x, xarray, sizeof(float) * N, cudaMemcpyHostToDevice);
    cudaMemcpy(device_y, yarray, sizeof(float) * N, cudaMemcpyHostToDevice);

    // Time GPU kernel execution separately
    double kernelStartTime = CycleTimer::currentSeconds();

    // run CUDA kernel. (notice the <<< >>> brackets indicating a CUDA
    // kernel launch) Execution on the GPU occurs here.
    saxpy_kernel<<<blocks, threadsPerBlock>>>(N, alpha, device_x, device_y, device_result);

    // Synchronize to ensure kernel completion before timing
    cudaDeviceSynchronize();
    double kernelEndTime = CycleTimer::currentSeconds();

    //
    // CS149 TODO: copy result from GPU back to CPU using cudaMemcpy
    //
    cudaMemcpy(resultarray, device_result, sizeof(float) * N, cudaMemcpyDeviceToHost);

    // end timing after result has been copied back into host memory
    double endTime = CycleTimer::currentSeconds();

    cudaError_t errCode = cudaPeekAtLastError();
    if (errCode != cudaSuccess) {
        fprintf(stderr, "WARNING: A CUDA error occured: code=%d, %s\n",
		errCode, cudaGetErrorString(errCode));
    }

    double overallDuration = endTime - startTime;
    double kernelDuration = kernelEndTime - kernelStartTime;

    // Calculate bandwidths
    float overallBandwidth = GBPerSec(totalBytes, overallDuration);

    // For GPU kernel bandwidth, only count GPU memory accesses (2 reads + 1 write per element)
    int gpuMemoryBytes = sizeof(float) * 3 * N; // Same as totalBytes for SAXPY
    float gpuKernelBandwidth = GBPerSec(gpuMemoryBytes, kernelDuration);

    // Get theoretical bandwidths
    float gpuMemoryBW = getGPUMemoryBandwidth();
    float pcieBW = getPCIeBandwidth();

    // Calculate efficiencies
    float overallEfficiency = (overallBandwidth / pcieBW) * 100.0f;
    float gpuEfficiency = (gpuKernelBandwidth / gpuMemoryBW) * 100.0f;

    // Print results
    printf("=== BANDWIDTH ANALYSIS ===\n");
    printf("Overall (CPU-GPU-CPU): %.3f ms\t[%.3f GB/s]\n", 1000.f * overallDuration, overallBandwidth);
    printf("GPU Kernel Only:       %.3f ms\t[%.3f GB/s]\n", 1000.f * kernelDuration, gpuKernelBandwidth);
    printf("\n");
    printf("PCIe Bandwidth (est):     %.1f GB/s\n", pcieBW);
    printf("GPU Memory Bandwidth:     %.1f GB/s\n", gpuMemoryBW);
    printf("\n");
    printf("PCIe Efficiency:          %.1f%%\n", overallEfficiency);
    printf("GPU Memory Efficiency:    %.1f%%\n", gpuEfficiency);

    //
    // CS149 TODO: free memory buffers on the GPU using cudaFree
    //
    cudaFree(device_x);
    cudaFree(device_y);
    cudaFree(device_result);

    return overallDuration;
}

void printCudaInfo() {

    // print out stats about the GPU in the machine.  Useful if
    // students want to know what GPU they are running on.

    int deviceCount = 0;
    cudaError_t err = cudaGetDeviceCount(&deviceCount);

    printf("---------------------------------------------------------\n");
    printf("Found %d CUDA devices\n", deviceCount);

    for (int i=0; i<deviceCount; i++) {
        cudaDeviceProp deviceProps;
        cudaGetDeviceProperties(&deviceProps, i);
        printf("Device %d: %s\n", i, deviceProps.name);
        printf("   SMs:        %d\n", deviceProps.multiProcessorCount);
        printf("   Global mem: %.0f MB\n",
               static_cast<float>(deviceProps.totalGlobalMem) / (1024 * 1024));
        printf("   CUDA Cap:   %d.%d\n", deviceProps.major, deviceProps.minor);
    }
    printf("---------------------------------------------------------\n");
}
